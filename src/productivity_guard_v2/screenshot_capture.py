"""Screenshot capture functionality for Productivity Guard V2."""

import os
from datetime import datetime
from pathlib import Path
from typing import Optional, List
import mss
from PIL import Image


class ScreenshotCapture:
    """Handles screenshot capture and storage (F-4, F-5)."""
    
    def __init__(self, config):
        """Initialize screenshot capture with configuration."""
        self.config = config
        self.sct = mss.mss()
        self.sequence_counter = 0
        self.current_date = None
        self._ensure_output_dir()
    
    def _ensure_output_dir(self) -> None:
        """Create output directory structure if it doesn't exist."""
        # Format: /output/screenshots/{YYYYMMMDD}/
        today = datetime.now()
        date_str = today.strftime("%Y%b%d").upper()  # e.g., "2025JUN15"
        
        # Reset sequence counter if date changed
        if self.current_date != date_str:
            self.current_date = date_str
            self.sequence_counter = 0
        
        self.daily_dir = Path(self.config.screenshot_output_dir) / date_str
        self.daily_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_monitors_to_capture(self) -> List[dict]:
        """Get list of monitors to capture based on configuration."""
        monitors = self.sct.monitors[1:]  # Skip the "All in One" monitor
        
        if self.config.screens_to_capture is None:
            return monitors
        
        selected_monitors = []
        for screen_idx in self.config.screens_to_capture:
            # Convert 1-based index to 0-based
            idx = screen_idx - 1
            if 0 <= idx < len(monitors):
                selected_monitors.append(monitors[idx])
            else:
                print(f"Warning: Screen {screen_idx} not available. Available screens: 1-{len(monitors)}")
        
        return selected_monitors or monitors  # Fallback to all if none valid
    
    def capture_screenshot(self) -> Optional[str]:
        """Capture screenshot and return the file path."""
        try:
            # Ensure directory exists (in case date changed)
            self._ensure_output_dir()
            
            monitors = self._get_monitors_to_capture()
            if not monitors:
                return None
            
            # Increment sequence counter
            self.sequence_counter += 1
            
            # Generate filename: {timestamp}-{seq}.png
            timestamp = datetime.now()
            timestamp_str = timestamp.strftime("%H%M%S")
            microseconds = timestamp.strftime("%f")[:6]
            seq_str = f"{self.sequence_counter:04d}"
            filename = f"{timestamp_str}-{microseconds}-{seq_str}.png"
            
            if len(monitors) == 1:
                # Single monitor
                screenshot = self.sct.grab(monitors[0])
                img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            else:
                # Multiple monitors - combine horizontally
                screenshots = []
                total_width = 0
                max_height = 0
                
                for monitor in monitors:
                    screenshot = self.sct.grab(monitor)
                    img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                    screenshots.append(img)
                    total_width += img.width
                    max_height = max(max_height, img.height)
                
                # Combine images
                combined = Image.new("RGB", (total_width, max_height))
                x_offset = 0
                for img in screenshots:
                    combined.paste(img, (x_offset, 0))
                    x_offset += img.width
                
                img = combined
            
            # Apply redaction if enabled
            if self.config.enable_redaction and self.config.redaction_regions:
                img = self._apply_redaction(img)
            
            # Save screenshot
            filepath = self.daily_dir / filename
            img.save(filepath, "PNG")
            
            return str(filepath)
            
        except Exception as e:
            print(f"Error capturing screenshot: {e}")
            return None
    
    def _apply_redaction(self, img: Image.Image) -> Image.Image:
        """Apply redaction to sensitive regions (F-14)."""
        try:
            from PIL import ImageDraw, ImageFilter
            
            # Create a copy to avoid modifying original
            redacted = img.copy()
            
            for region in self.config.redaction_regions:
                if len(region) == 4:  # (x1, y1, x2, y2)
                    x1, y1, x2, y2 = region
                    # Extract region, blur it, and paste back
                    region_img = redacted.crop((x1, y1, x2, y2))
                    blurred = region_img.filter(ImageFilter.GaussianBlur(radius=20))
                    redacted.paste(blurred, (x1, y1))
            
            return redacted
        except Exception as e:
            print(f"Error applying redaction: {e}")
            return img